Attribute VB_Name = "PINModule"
' ===================================================================
' FLOAT TRACKER SYSTEM - PIN MANAGEMENT MODULE
' Secure PIN storage, validation, and user management
' ===================================================================

Option Explicit

' ===================================================================
' PIN ENCODING/DECODING FUNCTIONS
' ===================================================================
Function EncodePIN(pin As String) As String
    Dim i As Integer
    Dim encodedPIN As String
    
    ' Simple encoding - shift each digit by 3 and add salt
    For i = 1 To Len(pin)
        encodedPIN = encodedPIN & Chr(Asc(Mid(pin, i, 1)) + 3)
    Next i
    
    ' Add simple salt
    EncodePIN = "FT" & encodedPIN & "XY"
End Function

Function DecodePIN(encodedPIN As String) As String
    Dim i As Integer
    Dim decodedPIN As String
    Dim cleanEncoded As String
    
    ' Remove salt
    cleanEncoded = Mid(encodedPIN, 3, Len(encodedPIN) - 4)
    
    ' Reverse the encoding
    For i = 1 To Len(cleanEncoded)
        decodedPIN = decodedPIN & Chr(Asc(Mid(cleanEncoded, i, 1)) - 3)
    Next i
    
    DecodePIN = decodedPIN
End Function

' ===================================================================
' PIN VALIDATION
' ===================================================================
Function ValidatePIN(staffName As String, enteredPIN As String) As Boolean
    On Error GoTo ErrorHandler
    
    Dim userDataSheet As Worksheet
    Dim lastRow As Long, i As Long
    Dim storedPIN As String
    
    Set userDataSheet = ThisWorkbook.Sheets("UserData")
    lastRow = userDataSheet.Cells(userDataSheet.Rows.Count, 1).End(xlUp).Row
    
    For i = 2 To lastRow
        If userDataSheet.Cells(i, 1).Value = staffName And _
           userDataSheet.Cells(i, 5).Value = "Active" Then
            
            ' Decode stored PIN and compare
            storedPIN = DecodePIN(userDataSheet.Cells(i, 2).Value)
            
            If storedPIN = enteredPIN Then
                ValidatePIN = True
                Exit Function
            End If
        End If
    Next i
    
    ValidatePIN = False
    Exit Function
    
ErrorHandler:
    ValidatePIN = False
End Function

' ===================================================================
' PIN GENERATION
' ===================================================================
Function GenerateRandomPIN() As String
    Dim pin As String
    Dim attempts As Integer
    
    Randomize ' Initialize random number generator
    
    Do
        ' Generate 4-digit random PIN
        pin = Format(Int(Rnd() * 9000) + 1000, "0000")
        attempts = attempts + 1
        
        ' Avoid obvious patterns and check for duplicates
        If pin <> "1234" And pin <> "0000" And pin <> "1111" And _
           pin <> "2222" And pin <> "3333" And pin <> "4444" And _
           pin <> "5555" And pin <> "6666" And pin <> "7777" And _
           pin <> "8888" And pin <> "9999" And pin <> "1212" And _
           pin <> "2121" And Not PINExists(pin) Then
            Exit Do
        End If
        
    Loop While attempts < 100
    
    If attempts >= 100 Then
        ' Fallback if we can't generate unique PIN
        pin = Format(Int(Rnd() * 9000) + 1000, "0000")
    End If
    
    GenerateRandomPIN = pin
End Function

Function PINExists(checkPIN As String) As Boolean
    On Error GoTo ErrorHandler
    
    Dim userDataSheet As Worksheet
    Dim lastRow As Long, i As Long
    
    Set userDataSheet = ThisWorkbook.Sheets("UserData")
    lastRow = userDataSheet.Cells(userDataSheet.Rows.Count, 1).End(xlUp).Row
    
    For i = 2 To lastRow
        If userDataSheet.Cells(i, 5).Value = "Active" Then
            If DecodePIN(userDataSheet.Cells(i, 2).Value) = checkPIN Then
                PINExists = True
                Exit Function
            End If
        End If
    Next i
    
    PINExists = False
    Exit Function
    
ErrorHandler:
    PINExists = False
End Function

' ===================================================================
' USER MANAGEMENT FUNCTIONS
' ===================================================================
Sub AddNewUser()
    On Error GoTo ErrorHandler
    
    Dim staffName As String
    Dim newPIN As String
    Dim userDataSheet As Worksheet
    Dim queueSheet As Worksheet
    Dim nextRow As Long
    Dim response As VbMsgBoxResult
    
    ' Get staff name from admin
    staffName = InputBox("Enter new staff member's name:", "Add New User")
    
    If staffName = "" Then Exit Sub
    
    ' Validate staff name
    If Len(staffName) < 2 Then
        MsgBox "Staff name must be at least 2 characters long!", vbExclamation
        Exit Sub
    End If
    
    ' Check if user already exists
    If UserExistsInUserData(staffName) Then
        MsgBox "User '" & staffName & "' already exists!", vbExclamation
        Exit Sub
    End If
    
    ' Generate random PIN
    newPIN = GenerateRandomPIN()
    
    ' Show PIN to admin ONE TIME ONLY
    MsgBox "NEW USER CREATED" & vbCrLf & vbCrLf & _
           "Staff Name: " & staffName & vbCrLf & _
           "PIN: " & newPIN & vbCrLf & vbCrLf & _
           "⚠️ WRITE THIS DOWN NOW!" & vbCrLf & _
           "This PIN will NOT be shown again!" & vbCrLf & vbCrLf & _
           "Give this PIN to " & staffName & " privately.", _
           vbExclamation, "One-Time PIN Display"
    
    ' Confirm admin wrote it down
    response = MsgBox("Have you written down the PIN and are ready to proceed?", vbYesNo + vbQuestion, "Confirm PIN Recorded")
    If response = vbNo Then
        MsgBox "User creation cancelled." & vbCrLf & vbCrLf & _
               "For security, the PIN was: " & newPIN & vbCrLf & _
               "Please write it down before clicking OK.", vbInformation
        Exit Sub
    End If
    
    ' Store user data securely
    Set userDataSheet = ThisWorkbook.Sheets("UserData")
    nextRow = userDataSheet.Cells(userDataSheet.Rows.Count, 1).End(xlUp).Row + 1
    
    With userDataSheet
        .Cells(nextRow, 1).Value = staffName
        .Cells(nextRow, 2).Value = EncodePIN(newPIN)  ' Encoded PIN
        .Cells(nextRow, 3).Value = Now()
        .Cells(nextRow, 4).Value = GetCurrentAdmin()
        .Cells(nextRow, 5).Value = "Active"
    End With
    
    ' Add to main queue at bottom
    Call AddUserToQueue(staffName)
    
    ' Log the user creation (without PIN)
    Call LogFloatAction(staffName, "", 0, "USER_CREATED", GetCurrentAdmin(), "New user added to system")
    
    ' Update colors
    Call UpdateQueueColors()
    
    MsgBox "User '" & staffName & "' successfully added to system!" & vbCrLf & _
           "They have been added to the bottom of the float queue.", vbInformation
    
    Exit Sub
    
ErrorHandler:
    MsgBox "Error adding new user: " & Err.Description, vbCritical
End Sub

Sub AddUserToQueue(staffName As String)
    Dim queueSheet As Worksheet
    Dim nextRow As Long
    
    Set queueSheet = ThisWorkbook.Sheets("Float Queue")
    nextRow = queueSheet.Cells(queueSheet.Rows.Count, 1).End(xlUp).Row + 1
    
    With queueSheet
        .Cells(nextRow, 1).Value = staffName        ' Staff Name
        .Cells(nextRow, 2).Value = ""               ' Last Floated (empty for new user)
        .Cells(nextRow, 3).Value = ""               ' Float Unit (empty for new user)
        .Cells(nextRow, 4).Value = "Active"         ' Status
        .Cells(nextRow, 5).Value = ""               ' Hold Reason (empty)
    End With
End Sub

Function UserExistsInUserData(staffName As String) As Boolean
    On Error GoTo ErrorHandler

    Dim userDataSheet As Worksheet
    Dim lastRow As Long, i As Long

    Set userDataSheet = ThisWorkbook.Sheets("UserData")
    lastRow = userDataSheet.Cells(userDataSheet.Rows.Count, 1).End(xlUp).Row

    For i = 2 To lastRow
        If userDataSheet.Cells(i, 1).Value = staffName And _
           userDataSheet.Cells(i, 5).Value = "Active" Then
            UserExistsInUserData = True
            Exit Function
        End If
    Next i

    UserExistsInUserData = False
    Exit Function

ErrorHandler:
    UserExistsInUserData = False
End Function

' ===================================================================
' ADDITIONAL USER MANAGEMENT FUNCTIONS
' ===================================================================
Sub ResetUserPIN()
    On Error GoTo ErrorHandler

    Dim staffName As String
    Dim newPIN As String
    Dim response As VbMsgBoxResult

    staffName = InputBox("Enter staff name to reset PIN:", "Reset PIN")

    If staffName = "" Then Exit Sub

    If Not UserExistsInUserData(staffName) Then
        MsgBox "User '" & staffName & "' not found!", vbExclamation
        Exit Sub
    End If

    ' Confirm the reset
    response = MsgBox("Are you sure you want to reset PIN for '" & staffName & "'?", vbYesNo + vbQuestion, "Confirm PIN Reset")
    If response = vbNo Then Exit Sub

    ' Generate new PIN
    newPIN = GenerateRandomPIN()

    ' Update stored PIN
    Call UpdateUserPIN(staffName, newPIN)

    ' Show new PIN to admin
    MsgBox "PIN RESET COMPLETED" & vbCrLf & vbCrLf & _
           "Staff Name: " & staffName & vbCrLf & _
           "New PIN: " & newPIN & vbCrLf & vbCrLf & _
           "⚠️ WRITE THIS DOWN NOW!" & vbCrLf & _
           "Give this new PIN to " & staffName & " privately.", _
           vbExclamation, "New PIN Generated"

    ' Log the PIN reset
    Call LogFloatAction(staffName, "", 0, "PIN_RESET", GetCurrentAdmin(), "PIN reset by admin")

    MsgBox "PIN reset completed for " & staffName, vbInformation

    Exit Sub

ErrorHandler:
    MsgBox "Error resetting PIN: " & Err.Description, vbCritical
End Sub

Sub UpdateUserPIN(staffName As String, newPIN As String)
    On Error GoTo ErrorHandler

    Dim userDataSheet As Worksheet
    Dim lastRow As Long, i As Long

    Set userDataSheet = ThisWorkbook.Sheets("UserData")
    lastRow = userDataSheet.Cells(userDataSheet.Rows.Count, 1).End(xlUp).Row

    For i = 2 To lastRow
        If userDataSheet.Cells(i, 1).Value = staffName And _
           userDataSheet.Cells(i, 5).Value = "Active" Then
            userDataSheet.Cells(i, 2).Value = EncodePIN(newPIN)
            Exit For
        End If
    Next i

    Exit Sub

ErrorHandler:
    MsgBox "Error updating PIN: " & Err.Description, vbCritical
End Sub

Sub RemoveUser()
    On Error GoTo ErrorHandler

    Dim staffName As String
    Dim response As VbMsgBoxResult

    staffName = InputBox("Enter staff name to remove:", "Remove User")

    If staffName = "" Then Exit Sub

    If Not UserExistsInUserData(staffName) Then
        MsgBox "User '" & staffName & "' not found!", vbExclamation
        Exit Sub
    End If

    response = MsgBox("Are you sure you want to remove '" & staffName & "'?" & vbCrLf & vbCrLf & _
                     "This will:" & vbCrLf & _
                     "• Remove them from the float queue" & vbCrLf & _
                     "• Deactivate their PIN" & vbCrLf & _
                     "• Keep their float history for records", _
                     vbYesNo + vbQuestion, "Confirm User Removal")

    If response = vbYes Then
        ' Mark as inactive (don't delete for audit trail)
        Call DeactivateUser(staffName)

        ' Remove from main queue
        Call RemoveUserFromQueue(staffName)

        ' Log the removal
        Call LogFloatAction(staffName, "", 0, "USER_REMOVED", GetCurrentAdmin(), "User removed from system")

        ' Update colors
        Call UpdateQueueColors()

        MsgBox "User '" & staffName & "' has been removed from the system.", vbInformation
    End If

    Exit Sub

ErrorHandler:
    MsgBox "Error removing user: " & Err.Description, vbCritical
End Sub

Sub DeactivateUser(staffName As String)
    Dim userDataSheet As Worksheet
    Dim lastRow As Long, i As Long

    Set userDataSheet = ThisWorkbook.Sheets("UserData")
    lastRow = userDataSheet.Cells(userDataSheet.Rows.Count, 1).End(xlUp).Row

    For i = 2 To lastRow
        If userDataSheet.Cells(i, 1).Value = staffName Then
            userDataSheet.Cells(i, 5).Value = "Inactive"
            Exit For
        End If
    Next i
End Sub

Sub RemoveUserFromQueue(staffName As String)
    Dim queueSheet As Worksheet
    Dim lastRow As Long, i As Long

    Set queueSheet = ThisWorkbook.Sheets("Float Queue")
    lastRow = queueSheet.Cells(queueSheet.Rows.Count, 1).End(xlUp).Row

    For i = 2 To lastRow
        If queueSheet.Cells(i, 1).Value = staffName Then
            queueSheet.Rows(i).Delete
            Exit For
        End If
    Next i
End Sub
